{# Shipment Update Success Fragment
   Context requirements:
   - acknowledgment: Success message text
   - shipmentId: ID of the updated shipment
   - updatedFields: Array of field names that were updated
   - updateData: Object containing the actual updated values
#}
{% if acknowledgment %}
{{ acknowledgment }}<br />
<br />
{% endif %}

Your shipment has been successfully updated with the following changes:<br />
<br />

{% if updateData.portCode %}
Port Code: <strong>{{ updateData.portCode }}</strong><br />
{% endif %}
{% if updateData.subLocation %}
Sub-location: <strong>{{ updateData.subLocation }}</strong><br />
{% endif %}
{% if updateData.cargoControlNumber %}
CCN: <strong>{{ updateData.cargoControlNumber }}</strong><br />
{% endif %}
{% if updateData.etaPort %}
ETA Port: <strong>{{ updateData.etaPort }}</strong><br />
{% endif %}
{% if updateData.etaDestination %}
ETA Destination: <strong>{{ updateData.etaDestination }}</strong><br />
{% endif %}
{% if updateData.hblNumber %}
HBL Number: <strong>{{ updateData.hblNumber }}</strong><br />
{% endif %}
{% if updateData.containerNumber %}
Container Number: <strong>{{ updateData.containerNumber }}</strong><br />
{% endif %}

<br />
These changes have been reflected in your shipment record.
