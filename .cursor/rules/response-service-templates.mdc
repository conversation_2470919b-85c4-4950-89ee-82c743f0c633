---
description:
globs: apps/portal-api/src/core-agent/services/shipment-response.service.ts,apps/portal-api/src/core-agent/templates/**/*.njk,apps/portal-api/src/core-agent/constants/templates.ts,apps/portal-api/src/core-agent/types/response-fragment.types.ts
alwaysApply: false
---

# Response Service & Template System

## Overview

Layer 3 of the shipment response architecture handles fragment rendering, deduplication, sorting, and final response assembly.

## Core Files

- **Response Service**: [apps/portal-api/src/core-agent/services/shipment-response.service.ts](mdc:apps/portal-api/src/core-agent/services/shipment-response.service.ts) (241 lines)
- **Template Constants**: [apps/portal-api/src/core-agent/constants/templates.ts](mdc:apps/portal-api/src/core-agent/constants/templates.ts)
- **Fragment Types**: [apps/portal-api/src/core-agent/types/response-fragment.types.ts](mdc:apps/portal-api/src/core-agent/types/response-fragment.types.ts)

## Response Service Features

### Fragment Processing Pipeline

```typescript
// Main method signature:
async renderFragments(
  fragments: ResponseFragment[],
  context: ShipmentContext
): Promise<string>

// Processing steps:
// 1. Deduplication (later fragments override earlier ones)
// 2. Sorting (explicit priority + template order)
// 3. Template rendering with context merging
// 4. Error handling with fallbacks
```

### Security & Error Handling

- **HTML Escaping**: Prevents injection attacks on user-provided content
- **Context Sanitization**: Removes service instances before template rendering
- **Individual Fragment Failures**: Don't break entire response
- **Fallback Responses**: For critical system failures

### Performance Features

- **Performance Monitoring**: Timing logs for optimization
- **Fragment Deduplication**: Reduces redundant rendering
- **Context Reuse**: Efficient memory usage

## Template System

### Template Directory Structure

```
apps/portal-api/src/core-agent/templates/
├── Status Templates (8):
│   ├── customs-status.njk ✅
│   ├── shipping-status.njk ✅
│   ├── shipping-status-unavailable.njk ✅
│   ├── submission-status.njk ✅
│   ├── release-status.njk ✅
│   ├── eta.njk ✅
│   ├── transaction-number.njk ✅
│   └── shipment-identifiers.njk ✅
├── Request Response Templates (9):
│   ├── rush-processing-success.njk ✅
│   ├── rush-processing-blocked.njk ✅
│   ├── cad-document-attached.njk ✅
│   ├── cad-document-not-ready.njk ✅
│   ├── rns-proof-attached.njk ✅
│   ├── rns-proof-not-ready.njk ✅
│   ├── manual-processing-requested.njk ✅
│   ├── hold-shipment-confirmed.njk ✅
│   └── documentation-coming-acknowledged.njk ✅
├── Compliance Templates (3):
│   ├── missing-documents-list.njk ✅
│   ├── compliance-errors.njk ✅
│   └── submission-required-notice.njk ✅
├── Support Templates (2):
│   ├── contact-support.njk ✅
│   └── system-unavailable.njk ✅
└── Generic Templates (5):
    ├── answer-question-template.njk ✅
    ├── answer-eta-template.njk ✅
    ├── answer-transaction-number-template.njk ✅
    ├── answer-release-status-template.njk ✅
    └── answer-shipping-status-template.njk ✅
```

### Template Priority System

From [TEMPLATE_ORDER](mdc:apps/portal-api/src/core-agent/constants/templates.ts):

```typescript
export const TEMPLATE_ORDER = [
  // Critical alerts (1-10)
  "rush-processing-success",
  "rush-processing-blocked",
  "manual-processing-requested",
  "hold-shipment-confirmed",
  "cad-document-attached",
  "rns-proof-attached",

  // Status information (11-20)
  "customs-status",
  "shipping-status",
  "submission-status",

  // Supporting information (21-30)
  "eta",
  "shipment-identifiers",
  "transaction-number",
  "release-status",

  // Specific answer templates (31-35)
  "answer-eta-template",
  "answer-transaction-number-template",
  "answer-release-status-template",
  "answer-shipping-status-template",

  // Compliance and errors (36-45)
  "compliance-errors",
  "missing-documents-list",
  "submission-required-notice",

  // Documentation not ready (46-55)
  "cad-document-not-ready",
  "rns-proof-not-ready",

  // Support and fallbacks (56+)
  "contact-support",
  "system-unavailable",
  "answer-question-template"
];
```

## Fragment Structure

```typescript
interface ResponseFragment {
  template: string; // Must match filename in templates/ (without .njk)
  priority?: number; // Optional explicit priority (overrides template order)
  fragmentContext?: object; // Optional additional context data
}
```

## Template Development

### Template Features

- **Nunjucks templating** with autoescape enabled
- **Context-aware conditional rendering** using shipment state
- **Safe null handling** for missing data
- **Clean HTML structure** with consistent styling

### Template Context Access

Templates receive the full `ShipmentContext` plus any `fragmentContext`:

```nunjucks
{# Access shipment data #}
{{ shipment.hblNumber }}
{{ shipment.cargoControlNumber }}

{# Access business rules #}
{% if isCompliant %}
  <p>Shipment is compliant</p>
{% else %}
  <p>Compliance issues detected</p>
{% endif %}

{# Access formatted data #}
{{ formattedCustomsStatus }}
{{ etaInformation.etaPortValue }}

{# Access side effects #}
{% if sideEffects.backofficeAlerts.rushProcessingSent %}
  <p>Alert has been sent to processing team</p>
{% endif %}

{# Safe null handling #}
{% if missingDocuments and missingDocuments.length > 0 %}
  <ul>
    {% for doc in missingDocuments %}
      <li>{{ doc }}</li>
    {% endfor %}
  </ul>
{% endif %}
```

### Specific Answer Templates (NEW)

GET_SHIPMENT_STATUS handler uses LLM-based question classification with specific answer templates:

```nunjucks
{# All specific answer templates follow this pattern #}
{# Context: { answer: string } #}
{{ answer }}
```

**Template Mapping**:

- ETA questions → `answer-eta-template` (Priority 1)
- Transaction number → `answer-transaction-number-template` (Priority 2)
- Release status → `answer-release-status-template` (Priority 3)
- Shipping status → `answer-shipping-status-template` (Priority 4)

**Context Structure**:

```typescript
{
  template: "answer-eta-template",
  priority: 1,
  fragmentContext: {
    answer: "The estimated time of arrival (ETA) is June 25, 2025."
  }
}
```

**Key Features**:

- **Simple structure**: Only requires `{ answer: string }` context
- **High priority**: Priorities 1-4 for specific answers vs 10+ for general status
- **LLM-generated content**: Answer text generated by handler based on shipment data
- **Fallback handling**: Handler provides appropriate messages for missing data

### Template Best Practices

1. **Always check for null/undefined** values
2. **Use conditional rendering** based on context state
3. **Keep templates focused** - one responsibility per template
4. **Use semantic HTML** with consistent styling
5. **Test with various shipment states**
6. **Compile templates**: Run `npm run build` after creating new templates

## Usage Patterns

### Basic Fragment Creation

```typescript
// Simple fragment (uses template order for priority)
const fragment: ResponseFragment = {
  template: "customs-status"
};

// Fragment with explicit priority
const fragment: ResponseFragment = {
  template: "rush-processing-success",
  priority: 1 // High priority - appears first
};

// Fragment with additional context
const fragment: ResponseFragment = {
  template: "custom-response",
  priority: 5,
  fragmentContext: {
    customData: "value",
    additionalInfo: context.someCalculation
  }
};
```

### Multi-Fragment Responses

```typescript
const fragments: ResponseFragment[] = [];

// Always include core status
fragments.push({ template: "customs-status", priority: 1 });

// Conditional additional information
if (context.etaInformation.etaPortValue) {
  fragments.push({ template: "eta", priority: 5 });
}

if (!context.isCompliant) {
  fragments.push({ template: "compliance-errors", priority: 10 });
}

// Supporting information
fragments.push({ template: "shipping-status", priority: 15 });

return fragments;
```

### Fragment Deduplication Behavior

```typescript
// Later fragments override earlier ones with same template name:
const fragments = [
  { template: "customs-status", priority: 1 },
  { template: "eta", priority: 5 },
  { template: "customs-status", priority: 2, fragmentContext: { extra: "data" } }
];

// Result: Only the last 'customs-status' fragment is rendered
// Final fragments: [eta, customs-status (with extra context)]
```

## Testing

### Response Service Test Controller

[ResponseServiceTestController](mdc:apps/portal-api/src/core-agent/controllers/test/response-service.test.controller.ts) provides:

```typescript
// Test fragment rendering with real data
POST /debug/response-service/render-fragments/:shipmentId

// Validate template exists and renders
GET /debug/response-service/validate-template/:templateName

// Test deduplication logic
GET /debug/response-service/deduplication-test

// Test priority sorting
GET /debug/response-service/priority-sorting-test

// System diagnostics
GET /debug/response-service/system-diagnostics
```

### Testing Workflow

1. **Template validation**: Ensure template renders without errors
2. **Fragment testing**: Test with sample fragment arrays
3. **Real data testing**: Use actual shipment IDs for integration testing
4. **Error scenarios**: Test with malformed fragments and missing templates

## Integration with Intent Handlers

### Handler Usage Pattern

```typescript
export class SomeIntentHandler extends BaseIntentHandler {
  async handle(validatedIntent: ValidatedIntent, context: ShipmentContext): Promise<ResponseFragment[]> {
    const fragments: ResponseFragment[] = [];

    // Use helper methods to build fragments
    fragments.push(this.createHighPriorityFragment("success-response"));

    if (!context.isCompliant) {
      this.addComplianceFragmentsIfNeeded(fragments, context, 10);
    }

    return fragments;
  }
}
```

### Response Service Integration

```typescript
// In email processors (future integration):
const context = await this.contextService.buildContext(shipmentId, organizationId);
const allFragments: ResponseFragment[] = [];

// Process each intent
for (const validatedIntent of email.userIntents) {
  const handler = this.intentHandlerRegistry.getHandler(validatedIntent.intent);
  if (handler) {
    const fragments = await handler.handle(validatedIntent, context);
    allFragments.push(...fragments);
  }
}

// Render final response
const response = await this.responseService.renderFragments(allFragments, context);
```

## Error Handling

### Template Rendering Failures

- Individual template failures create error fragments
- System continues processing other fragments
- Error fragments include user-friendly messages
- Full system failures return fallback responses

### Debugging Failed Templates

```typescript
// Check template validation:
GET / debug / response - service / validate - template / template - name;

// Check system diagnostics:
GET / debug / response - service / system - diagnostics;

// Review logs for template rendering errors
```

## Performance Considerations

### Optimization Features

- **Map-based deduplication**: O(n) time complexity
- **Streaming fragment processing**: No large array building
- **Context reuse**: Shared across multiple fragments
- **Template caching**: Nunjucks handles template compilation caching

### Performance Monitoring

```typescript
// Response service logs timing information:
// "Rendering 5 fragments for shipment 12345"
// "Rendered 3 fragments in 45ms"
```

## Development Guidelines

1. **Template Naming**: Use kebab-case, descriptive names
2. **Priority Planning**: Use appropriate priorities for response flow
3. **Error Resilience**: Design templates to handle missing data gracefully
4. **Context Efficiency**: Don't pass unnecessary data in fragmentContext
5. **Testing**: Always test templates with various shipment states
6. **Documentation**: Update TEMPLATE_ORDER when adding new templates
