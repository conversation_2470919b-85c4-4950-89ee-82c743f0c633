---
description:
globs: apps/portal-api/src/core-agent/handlers/**/*.ts,apps/portal-api/src/core-agent/services/intent-handler-registry.service.ts,apps/portal-api/src/core-agent/interfaces/intent-handler.interface.ts
alwaysApply: false
---

# Intent Handler Development Guide

## Overview

Intent handlers are Layer 2 of the response architecture. They process validated user intents and return response fragments based on shipment context.

## Core Files

- **Base Handler**: [apps/portal-api/src/core-agent/handlers/base-intent-handler.ts](mdc:apps/portal-api/src/core-agent/handlers/base-intent-handler.ts)
- **Interface**: [apps/portal-api/src/core-agent/interfaces/intent-handler.interface.ts](mdc:apps/portal-api/src/core-agent/interfaces/intent-handler.interface.ts)
- **Registry**: [apps/portal-api/src/core-agent/services/intent-handler-registry.service.ts](mdc:apps/portal-api/src/core-agent/services/intent-handler-registry.service.ts)

## Handler Implementation Pattern

### Basic Structure

```typescript
@Injectable()
export class YourIntentHandler extends BaseIntentHandler {
  readonly classificationMeta: IntentClassificationMeta = {
    intent: "YOUR_EMAIL_INTENT" as const,
    description: "Clear description of what this handler does",
    examples: ["Example user request 1", "Example user request 2", "Example user request 3"],
    keywords: ["keyword1", "keyword2"] // Optional
  };

  async handle(validatedIntent: ValidatedIntent, context: ShipmentContext): Promise<ResponseFragment[]> {
    const fragments: ResponseFragment[] = [];

    // Your handler logic here

    return fragments;
  }
}
```

## Existing Handlers (Reference Examples)

### Status Handler (Enhanced with LLM Classification)

[GetShipmentStatusHandler](mdc:apps/portal-api/src/core-agent/handlers/get-shipment-status.handler.ts) - Advanced handler with LLM-based question classification and specific answer templates

**Key Features**:

- **LLM Question Classification**: Uses `AnswerUserQueryService.classifyQuestion()` to categorize questions
- **Specific Answer Templates**: Returns targeted responses for ETA, transaction number, release status, shipping status
- **Priority-based Ordering**: Specific answers (priority 1-4) appear before general status (priority 10+)
- **Fallback Handling**: Graceful degradation when data is missing

**Question Classification Types**:

```typescript
type QuestionType =
  | "ETA" // "When will it arrive?"
  | "TRANSACTION_NUMBER" // "What's the transaction number?"
  | "RELEASE_STATUS" // "Has it been released?"
  | "SHIPPING_STATUS" // "What's the shipping status?"
  | "GENERAL_STATUS" // "What's the status?"
  | "CUSTOMS_STATUS"; // Customs-specific questions
```

**Template Mapping**:

- ETA → `answer-eta-template` (Priority 1)
- Transaction number → `answer-transaction-number-template` (Priority 2)
- Release status → `answer-release-status-template` (Priority 3)
- Shipping status → `answer-shipping-status-template` (Priority 4)
- General status → `core-agent/fragments/status-message` + `core-agent/fragments/details` (Priority 10-11)

**Testing Commands**:

```bash
# Test specific question types
node src/core-agent/testing/test-processor-direct.js --intent=GET_SHIPMENT_STATUS --instructions="What is the ETA?" --verbose
node src/core-agent/testing/test-processor-direct.js --intent=GET_SHIPMENT_STATUS --instructions="What is the transaction number?" --verbose
node src/core-agent/testing/test-processor-direct.js --intent=GET_SHIPMENT_STATUS --instructions="Has the shipment been released?" --verbose
node src/core-agent/testing/test-processor-direct.js --intent=GET_SHIPMENT_STATUS --instructions="What is the shipping status?" --verbose
```

### Document Request Handler

[RequestCADDocumentHandler](mdc:apps/portal-api/src/core-agent/handlers/request-cad-document.handler.ts) - Handles document generation with side effects

### Processing Request Handler

[RequestRushProcessingHandler](mdc:apps/portal-api/src/core-agent/handlers/request-rush-processing.handler.ts) - Validation logic with backoffice alerts

### Acknowledgment Handler

[DocumentationComingHandler](mdc:apps/portal-api/src/core-agent/handlers/documentation-coming.handler.ts) - Simple acknowledgment response

## Available Helper Methods (from BaseIntentHandler)

### Context Modification

```typescript
// Mark that user explicitly asked for something
this.markAsDirectlyAsked(context, "shipment_status");

// Add compliance fragments if issues exist
this.addComplianceFragmentsIfNeeded(fragments, context, priority);

// Add submission required notice
this.addSubmissionRequiredNoticeIfNeeded(fragments, context, priority);
```

### Side Effects

```typescript
// Send backoffice alert
const success = await this.sendBackofficeAlert(
  "Rush Processing Request",
  shipmentId,
  userInstructions,
  context
);
```

### Fragment Creation Helpers

```typescript
// High priority (appears first)
const fragment = this.createHighPriorityFragment("template-name", { data });

// Medium priority (standard responses)
const fragment = this.createMediumPriorityFragment("template-name", { data });

// Low priority (supplementary info)
const fragment = this.createLowPriorityFragment("template-name", { data });

// Error handling
const errorFragment = this.createErrorFragment("Custom error message");
```

### Safe Execution

```typescript
const { success, result, error } = await this.safeExecute(async () => {
  // Potentially failing operation
  return await someAsyncOperation();
}, "Context for error logging");
```

## EMAIL_INTENTS Mapping

From [apps/portal-api/src/email/types/ai-agent.types.ts](mdc:apps/portal-api/src/email/types/ai-agent.types.ts):

### ✅ Implemented Handlers

- `GET_SHIPMENT_STATUS` → [GetShipmentStatusHandler](mdc:apps/portal-api/src/core-agent/handlers/get-shipment-status.handler.ts)
- `REQUEST_CAD_DOCUMENT` → [RequestCADDocumentHandler](mdc:apps/portal-api/src/core-agent/handlers/request-cad-document.handler.ts)
- `REQUEST_RUSH_PROCESSING` → [RequestRushProcessingHandler](mdc:apps/portal-api/src/core-agent/handlers/request-rush-processing.handler.ts)
- `DOCUMENTATION_COMING` → [DocumentationComingHandler](mdc:apps/portal-api/src/core-agent/handlers/documentation-coming.handler.ts)

### 🔄 Missing Handlers (Need Implementation)

- `PROCESS_DOCUMENT` - **HIGH PRIORITY** (handles CREATE/UPDATE intents)
- `REQUEST_RNS_PROOF` - **HIGH PRIORITY** (common request)
- `REQUEST_MANUAL_PROCESSING` - Manual processing requests
- `REQUEST_HOLD_SHIPMENT` - Hold shipment requests
- `CREATE_SHIPMENT` - Or map to PROCESS_DOCUMENT
- `UPDATE_SHIPMENT` - Or map to PROCESS_DOCUMENT
- `CREATE_COMMERCIAL_INVOICE` - Or map to PROCESS_DOCUMENT
- `UPDATE_COMMERCIAL_INVOICE` - Or map to PROCESS_DOCUMENT
- `CREATE_CERTIFICATE_OF_ORIGIN` - Or map to PROCESS_DOCUMENT
- `UPDATE_CERTIFICATE_OF_ORIGIN` - Or map to PROCESS_DOCUMENT
- `UNSORTED` - Fallback handler
- `UNKNOWN` - Unclear intent handler
- `SPAM` - Spam detection handler

## Handler Registration

### ❌ CRITICAL ISSUE - Handler Registration Broken

Current registration in [CoreAgentModule](mdc:apps/portal-api/src/core-agent/core-agent.module.ts):

```typescript
{
  provide: "INTENT_HANDLERS",
  useValue: []  // ❌ Empty array - no handlers work!
}
```

### ✅ Required Fix

```typescript
{
  provide: "INTENT_HANDLERS",
  useFactory: () => [
    new GetShipmentStatusHandler(),
    new RequestCADDocumentHandler(),
    new RequestRushProcessingHandler(),
    new DocumentationComingHandler(),
    // Add new handlers here
  ]
}
```

## Development Workflow

### 1. Create Handler File

```bash
# Create new handler in handlers directory
touch apps/portal-api/src/core-agent/handlers/your-intent.handler.ts
```

### 2. Implement Handler

```typescript
// Follow the pattern above
// Extend BaseIntentHandler
// Define classificationMeta
// Implement handle() method
```

### 3. Register Handler

Add to `INTENT_HANDLERS` provider in [CoreAgentModule](mdc:apps/portal-api/src/core-agent/core-agent.module.ts)

### 4. Test Handler

Use [ResponseServiceTestController](mdc:apps/portal-api/src/core-agent/controllers/test/response-service.test.controller.ts):

- Test handler registration
- Test fragment generation
- Test with real shipment data

## Template Integration

### Available Templates

All templates in [apps/portal-api/src/core-agent/templates/](mdc:apps/portal-api/src/core-agent/templates)

### Template Priority System

Use priorities from [TEMPLATE_ORDER](mdc:apps/portal-api/src/core-agent/constants/templates.ts):

- 1-10: Critical alerts
- 11-20: Status information
- 21-30: Supporting information
- 31-40: Compliance and errors
- 41-50: Documentation not ready
- 51+: Support and fallbacks

### Fragment Structure

```typescript
interface ResponseFragment {
  template: string; // Template name (must exist in templates/)
  priority?: number; // Optional explicit priority
  fragmentContext?: object; // Optional additional context for template
}
```

## Business Logic Access

### Pre-computed Context Properties

```typescript
// Always use context properties, never duplicate business logic:
if (context.canRush) {
  // Rush processing logic
}

if (!context.isCompliant) {
  // Add compliance fragments
}

if (context.missingDocuments.length > 0) {
  // Handle missing documents
}
```

### Service Access

```typescript
// Use resolved service instances:
await context._services.emailService.sendAlert();
await context._services.rnsStatusChangeEmailSender.send();
```

## Testing and Debugging

### Registry Diagnostics

```typescript
// Check handler registration:
GET / debug / core - agent / intent - handlers;
GET / debug / response - service / system - diagnostics;
```

### Fragment Testing

```typescript
// Test fragment rendering:
POST /debug/response-service/render-fragments/:shipmentId
```

## Best Practices

1. **Single Responsibility**: Each handler handles one EMAIL_INTENT type
2. **Use Helper Methods**: Leverage BaseIntentHandler helpers for common tasks
3. **Fragment Composition**: Build responses from multiple focused fragments
4. **Context First**: Always check context properties before complex logic
5. **Error Handling**: Use safeExecute for operations that might fail
6. **Classification Quality**: Provide clear descriptions and good examples
7. **Priority Planning**: Use appropriate fragment priorities for response flow

## Common Patterns

### Conditional Fragment Addition

```typescript
if (context.someCondition) {
  fragments.push({ template: "some-template", priority: 10 });
}
```

### Multi-Fragment Responses

```typescript
// Always include core information
fragments.push({ template: "core-status", priority: 1 });

// Add conditional supplementary information
if (context.hasETA) {
  fragments.push({ template: "eta-info", priority: 5 });
}

if (!context.isCompliant) {
  this.addComplianceFragmentsIfNeeded(fragments, context, 10);
}
```

### Side Effect + Response Pattern

```typescript
// Perform side effect
const alertSent = await this.sendBackofficeAlert(alertType, shipmentId, instructions, context);

// Update context to reflect side effect
if (alertSent) {
  context.sideEffects.backofficeAlerts[alertType] = true;
}

// Return appropriate fragment
fragments.push({
  template: alertSent ? "success-template" : "failure-template",
  priority: 1
});
```
