---
description:
globs: apps/portal-api/src/core-agent/templates/**/*.njk
alwaysApply: false
---

# Template Development Guide

## Template Organization

### Directory Structure

Templates are organized in `apps/portal-api/src/core-agent/templates/` with logical groupings:

```
templates/
├── status/             # Status information templates
│   ├── customs-status.njk
│   ├── shipping-status.njk
│   ├── submission-status.njk
│   ├── release-status.njk
│   ├── eta.njk
│   ├── transaction-number.njk
│   └── shipment-identifiers.njk
├── requests/           # Request confirmation templates
│   ├── rush-processing-success.njk
│   ├── rush-processing-blocked.njk
│   ├── cad-document-attached.njk
│   ├── cad-document-not-ready.njk
│   ├── rns-proof-attached.njk (missing)
│   ├── rns-proof-not-ready.njk (missing)
│   ├── manual-processing-requested.njk (missing)
│   └── hold-shipment-confirmed.njk (missing)
├── compliance/         # Compliance and error templates
│   ├── compliance-errors.njk
│   ├── missing-documents-list.njk
│   └── submission-required-notice.njk
└── shared/            # Reusable templates
    ├── answer-question-template.njk
    ├── answer-eta-template.njk ✅
    ├── answer-transaction-number-template.njk ✅
    ├── answer-release-status-template.njk ✅
    ├── answer-shipping-status-template.njk ✅
    ├── contact-support.njk (missing)
    └── system-unavailable.njk (missing)
```

## Template Naming Convention

### Naming Rules

- **Use kebab-case**: `rush-processing-success.njk`
- **Include state**: `-success`, `-blocked`, `-not-ready`, `-attached`
- **Be descriptive**: Template name should indicate purpose
- **Group by function**: Place in appropriate subdirectory

### State Naming Pattern

```
{action}-{result}.njk
- rush-processing-success.njk      # Successful rush request
- rush-processing-blocked.njk      # Blocked rush request
- cad-document-attached.njk        # CAD document provided
- cad-document-not-ready.njk       # CAD not available yet
```

## Template Context Safety

### Null/Undefined Checks

Always check for null/undefined values before using them:

```nunjucks
{# Check for object existence #}
{% if shipment and shipment.cargoControlNumber %}
  <p>CCN: {{ shipment.cargoControlNumber }}</p>
{% endif %}

{# Check for nested properties #}
{% if shipment and shipment.portOfDischarge and shipment.portOfDischarge.name %}
  <p>Port: {{ shipment.portOfDischarge.name }}</p>
{% endif %}

{# Check for arrays #}
{% if containers and containers.length > 0 %}
  <p>Containers: {{ containers.join(', ') }}</p>
{% endif %}
```

### Array Iteration Safety

```nunjucks
{# Safe array iteration #}
{% if missingDocuments and missingDocuments.length > 0 %}
  <div>
    <h5>Missing Documents:</h5>
    <ul>
      {% for doc in missingDocuments %}
        <li>{{ doc }}</li>
      {% endfor %}
    </ul>
  </div>
{% endif %}

{# Empty state handling #}
{% if complianceErrors and complianceErrors.length > 0 %}
  {% for error in complianceErrors %}
    <p>{{ error }}</p>
  {% endfor %}
{% else %}
  <p>No compliance issues found.</p>
{% endif %}
```

### Date Formatting Safety

```nunjucks
{# Safe date handling #}
{% if etaInformation and etaInformation.etaPortValue %}
  <p>ETA: {{ etaInformation.etaPortValue }}</p>
{% else %}
  <p>ETA: TBD</p>
{% endif %}

{# Use formatted dates from context #}
{% if timing and timing.formattedEtaPort %}
  <p>Expected arrival: {{ timing.formattedEtaPort }}</p>
{% endif %}
```

## Template Content Patterns

### Standard HTML Structure

```nunjucks
{# rush-processing-success.njk #}
<p>We've received your rush processing request and have forwarded it to our team for immediate attention.</p>

{% if sideEffects and sideEffects.backofficeAlerts and sideEffects.backofficeAlerts.rushProcessingSent %}
<p>Your request has been escalated and you should receive an update within 2 business hours.</p>
{% endif %}

{% if identifiers and identifiers.ccn %}
<p><strong>Reference:</strong> {{ identifiers.ccn }}</p>
{% endif %}
```

### Conditional Content

```nunjucks
{# rush-processing-blocked.njk #}
<p>We've received your rush processing request, however this shipment cannot currently be expedited.</p>

{% if rushBlockingReason %}
<p><strong>Reason:</strong> {{ rushBlockingReason }}</p>
{% endif %}

{% if not isCompliant %}
<p>Please resolve any compliance issues first, then submit a new rush request.</p>
{% endif %}

{% if missingDocuments and missingDocuments.length > 0 %}
<p>Missing documents must be provided before rush processing can begin.</p>
{% endif %}
```

### List Templates

```nunjucks
{# missing-documents-list.njk #}
{% if missingDocuments and missingDocuments.length > 0 %}
<div>
  <h5>Missing Documents:</h5>
  <ul>
    {% for doc in missingDocuments %}
      <li>{{ doc }}</li>
    {% endfor %}
  </ul>
  <p>Please upload these documents to proceed with processing.</p>
</div>
{% endif %}
```

## Context Usage Patterns

### Core Context Properties

Templates have access to the full ShipmentContext:

```nunjucks
{# Raw data #}
{{ shipment.cargoControlNumber }}
{{ compliance.noCommercialInvoice }}
{{ organization.name }}

{# Business rule evaluations #}
{% if canRush %}...{% endif %}
{% if isCompliant %}...{% endif %}
{% if canGenerateCAD %}...{% endif %}

{# Formatted display values #}
{{ formattedCustomsStatus }}
{{ shipmentIdentifiers.hblNumber }}
{{ etaInformation.etaPortValue }}

{# Enhanced context #}
{{ documentDataStatus.hblStatus }}
{{ missingFieldsAnalysis.formattedMissingFields }}
{{ templateContext.timing.formattedEtaPort }}
```

### Side Effects Context

```nunjucks
{# Check for side effects #}
{% if sideEffects and sideEffects.cadDocument %}
<p>CAD document has been generated and attached.</p>
{% endif %}

{% if sideEffects and sideEffects.backofficeAlerts and sideEffects.backofficeAlerts.rushProcessingSent %}
<p>Rush processing alert sent to operations team.</p>
{% endif %}
```

### Fragment Context Override

```typescript
// Fragment with additional context
{
  template: 'compliance-errors',
  priority: 2,
  fragmentContext: {
    customError: 'Specific error message',
    showDetails: true
  }
}
```

```nunjucks
{# compliance-errors.njk - uses fragment context #}
{% if customError %}
  <p><strong>Error:</strong> {{ customError }}</p>
{% endif %}

{% if showDetails and complianceErrors %}
  <div>
    <h5>Details:</h5>
    {% for error in complianceErrors %}
      <p>{{ error }}</p>
    {% endfor %}
  </div>
{% endif %}
```

## HTML Formatting Guidelines

### Clean HTML Structure

```nunjucks
{# Use semantic HTML #}
<div class="shipment-status">
  <h4>Shipment Status</h4>
  <p>{{ formattedCustomsStatus }}</p>

  {% if isSubmitted %}
    <p><strong>Status:</strong> Submitted to customs</p>
  {% endif %}
</div>

{# Use appropriate tags #}
<ul>
  {% for container in containers %}
    <li>Container: {{ container }}</li>
  {% endfor %}
</ul>
```

### Consistent Styling Classes

```nunjucks
{# Use consistent CSS classes if available #}
<div class="alert alert-info">
  <p>Your shipment is being processed.</p>
</div>

<div class="status-update">
  <h5>Latest Update</h5>
  <p>{{ formattedCustomsStatus }}</p>
</div>
```

## Template Priority Guidelines

### Priority Ranges

Use these priority ranges for consistent ordering:

```
1-10:   Critical alerts (rush-processing-success, cad-document-attached)
11-20:  Status information (customs-status, shipping-status)
21-30:  Supporting information (eta, shipment-identifiers)
31-40:  Compliance and errors (compliance-errors, missing-documents-list)
41-50:  Not ready messages (cad-document-not-ready)
51+:    Support and fallbacks (contact-support, system-unavailable)
```

### Priority Assignment

```typescript
// Explicit priority in fragments
{ template: 'rush-processing-success', priority: 5 }
{ template: 'customs-status', priority: 15 }
{ template: 'compliance-errors', priority: 35 }

// Falls back to predefined template order if no priority
{ template: 'eta' } // Uses predefined position (~25)
```

## Error Handling in Templates

### Graceful Degradation

```nunjucks
{# Handle missing data gracefully #}
{% if shipmentIdentifiers %}
  {% if shipmentIdentifiers.hblNumber %}
    <p>HBL: {{ shipmentIdentifiers.hblNumber }}</p>
  {% endif %}
  {% if shipmentIdentifiers.cargoControlNumber %}
    <p>CCN: {{ shipmentIdentifiers.cargoControlNumber }}</p>
  {% endif %}
{% else %}
  <p>Shipment reference information not available.</p>
{% endif %}
```

### Fallback Content

```nunjucks
{# Provide fallback content #}
{% if formattedCustomsStatus %}
  <p>Status: {{ formattedCustomsStatus }}</p>
{% else %}
  <p>Status: Information being updated</p>
{% endif %}

{# Conditional sections #}
{% if etaInformation and etaInformation.etaPortValue %}
  <p>Expected arrival: {{ etaInformation.etaPortValue }}</p>
{% elif timing and timing.formattedEtaPort and timing.formattedEtaPort !== 'TBD' %}
  <p>Expected arrival: {{ timing.formattedEtaPort }}</p>
{% else %}
  <p>Arrival time will be updated once available.</p>
{% endif %}
```

## Specific Answer Templates

### GET_SHIPMENT_STATUS Answer Templates

New specific answer templates for targeted responses:

```nunjucks
{# answer-eta-template.njk #}
{# ETA-specific answer template #}
{# Context: { answer: string } #}
{{ answer }}

{# answer-transaction-number-template.njk #}
{# Transaction number-specific answer template #}
{# Context: { answer: string } #}
{{ answer }}

{# answer-release-status-template.njk #}
{# Release status-specific answer template #}
{# Context: { answer: string } #}
{{ answer }}

{# answer-shipping-status-template.njk #}
{# Shipping status-specific answer template #}
{# Context: { answer: string } #}
{{ answer }}
```

### Specific Answer Context Pattern

All specific answer templates use this standardized context:

```typescript
{
  template: "answer-eta-template",
  priority: 1,
  fragmentContext: {
    answer: "The estimated time of arrival (ETA) is June 25, 2025."
  }
}
```

**Key Features**:

- **Simple structure**: Only requires `{ answer: string }` context
- **High priority**: Priorities 1-4 for specific answers vs 10+ for general status
- **Fallback handling**: Handler generates appropriate fallback messages for missing data
- **LLM-generated content**: Answer text is generated by LLM based on available shipment data

### Template Compilation Requirement

**CRITICAL**: New templates must be compiled before use:

```bash
# Always run after creating new templates
cd apps/portal-api
npm run build

# Verify templates exist in dist directory
ls -la dist/core-agent/templates/answer-*-template.njk
```

## Template Testing

### Test with Debug Controller

Use `ResponseServiceTestController` endpoints:

```bash
# Validate template syntax
GET /debug/response-service/validate-template/rush-processing-success

# Test with real shipment data
POST /debug/response-service/render-fragments/123456
{
  "fragments": [{"template": "rush-processing-success", "priority": 1}],
  "organizationId": 1
}
```

### Test Specific Answer Templates

Test GET_SHIPMENT_STATUS specific templates:

```bash
# Test ETA template
node src/core-agent/testing/test-processor-direct.js --intent=GET_SHIPMENT_STATUS --instructions="What is the ETA?" --verbose

# Test transaction number template
node src/core-agent/testing/test-processor-direct.js --intent=GET_SHIPMENT_STATUS --instructions="What is the transaction number?" --verbose

# Test release status template
node src/core-agent/testing/test-processor-direct.js --intent=GET_SHIPMENT_STATUS --instructions="Has the shipment been released?" --verbose

# Test shipping status template
node src/core-agent/testing/test-processor-direct.js --intent=GET_SHIPMENT_STATUS --instructions="What is the shipping status?" --verbose
```

### Sample Context Testing

```typescript
// Create test context for template validation
const sampleContext = {
  shipment: { cargoControlNumber: "TEST123", customsStatus: "pending" },
  compliance: { noCommercialInvoice: false, missingFields: [] },
  canRush: true,
  isCompliant: false,
  formattedCustomsStatus: "Pending Review"
};
```

## Security Considerations

### HTML Escaping

- Context sanitization happens at the service level
- User-provided content is escaped before reaching templates
- Avoid using `| safe` filter unless absolutely necessary

### Content Validation

```nunjucks
{# Avoid direct HTML injection #}
<p>{{ userMessage }}</p>  {# Safe - auto-escaped #}

{# Instead of #}
<p>{{ userMessage | safe }}</p>  {# Dangerous - avoid #}
```

## Template Maintenance

### Adding New Templates

1. Create template file in appropriate subdirectory
2. Follow naming convention (kebab-case with state)
3. Add to template priority order if needed
4. Test with debug controller
5. Update documentation

### Template Refactoring

- Keep templates focused on single responsibility
- Extract common patterns to shared templates if needed
- Maintain backward compatibility with existing context
- Update tests when changing template structure
